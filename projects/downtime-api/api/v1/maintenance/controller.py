from typing import TYPE_CHECKING
import re
from datetime import datetime, timezone
from uuid import uuid4
from pynamodb.expressions.condition import Condition
from aws_lambda_powertools.event_handler.exceptions import (
    NotFoundError,
)

from context import Context
from core.models import MaintenancePeriod
from api.v1.maintenance.schemas import (
    GetMaintenancePeriodByIdRequest,
    GetMaintenancePeriodsRequest,
    GetMaintenancePeriodResponse,
    CreateMaintenancePeriodRequest,
    CreateMaintenancePeriodResponse,
    UpdateMaintenancePeriodRequest,
    UpdateMaintenancePeriodResponse,
    DeleteMaintenancePeriodRequest,
    DeleteMaintenancePeriodResponse,
)


if TYPE_CHECKING: # Prevent circular import
    from context import Context


class MaintenanceController:
    def __init__(self, context: "Context") -> None:
        self._context = context

    def get_maintenance_period_by_id(self, request: GetMaintenancePeriodByIdRequest) -> GetMaintenancePeriodResponse:
        period = MaintenancePeriod.get(request.id)
        if not period:
            raise NotFoundError(f"Could not find maintenance period with id {request.id}")
        return GetMaintenancePeriodResponse.model_validate(period)

    def get_maintenance_periods(self, request: GetMaintenancePeriodsRequest) -> list[GetMaintenancePeriodResponse]:
        scan_conditions: Condition | None = None

        if request.active:
            now = datetime.now(timezone.utc)
            scan_conditions = (MaintenancePeriod.start <= now) & (MaintenancePeriod.end >= now) if MaintenancePeriod.end else (MaintenancePeriod.start <= now)

        periods = MaintenancePeriod.scan(filter_condition=scan_conditions)
        if request.site_id:
            matched_periods = []
            for period in periods:
                if period.site_id_regex and re.match(period.site_id_regex, request.site_id):
                    matched_periods.append(period)
            results = matched_periods
        else:
            results = list(periods)

        return [GetMaintenancePeriodResponse.model_validate(period) for period in results]

    def create_maintenance_period(self, request: CreateMaintenancePeriodRequest) -> CreateMaintenancePeriodResponse:
        period = MaintenancePeriod(
            id=str(uuid4()),
            start=request.start,
            end=request.end,
            site_id_regex=request.site_id_regex,
        )
        period.save()
        return CreateMaintenancePeriodResponse(id=period.id)

    def update_maintenance_period(self, request: UpdateMaintenancePeriodRequest) -> UpdateMaintenancePeriodResponse:
        period = MaintenancePeriod.get(request.id)
        if not period:
            raise NotFoundError(f"Could not find maintenance period with id {request.id}")
        
        actions = []

        if request.start:
            actions.append(MaintenancePeriod.start.set(request.start))
        if request.end:
            actions.append(MaintenancePeriod.end.set(request.end))
        if request.site_id_regex:
            actions.append(MaintenancePeriod.site_id_regex.set(request.site_id_regex))

        period.update(actions=actions)
        return UpdateMaintenancePeriodResponse(id=period.id)

    def delete_maintenance_period(self, request: DeleteMaintenancePeriodRequest) -> DeleteMaintenancePeriodResponse:
        period = MaintenancePeriod.get(request.id)
        if not period:
            raise NotFoundError(f"Could not find maintenance period with id {request.id}")
        
        period.delete()
        return DeleteMaintenancePeriodResponse(id=period.id)