from datetime import datetime
from pydantic import BaseModel, Field


# --- Create Maintenance Period --- #

class CreateMaintenancePeriodRequest(BaseModel):
    start: datetime = Field(description="Start of the maintenance period")
    end: datetime | None = Field(description="End of the maintenance period")
    site_id_regex: str | None = Field(None, description="Regex to match site IDs to apply the maintenance period to")


class CreateMaintenancePeriodResponse(BaseModel):
    id: str = Field(description="ID of the created maintenance period")


# --- Get Maintenance Period --- #


class GetMaintenancePeriodByIdRequest(BaseModel):
    id: str = Field(description="ID of the maintenance period to get")


class GetMaintenancePeriodsRequest(BaseModel):
    site_id: str | None = Field(None, description="Filter by site ID (supports regex matching).")
    active: bool | None = Field(False, description="Filter for currently active maintenance periods.")


class GetMaintenancePeriodResponse(BaseModel):
    id: str = Field(description="ID of the maintenance period")
    start: datetime = Field(description="Start of the maintenance period")
    end: datetime | None = Field(description="End of the maintenance period")
    site_id_regex: str | None = Field(description="Regex to match site IDs to apply the maintenance period to")


# --- Update Maintenance Period --- #

class UpdateMaintenancePeriodRequest(BaseModel):
    id: str = Field(description="ID of the maintenance period to update")
    start: datetime | None = Field(description="Start of the maintenance period")
    end: datetime | None = Field(description="End of the maintenance period")
    site_id_regex: str | None = Field(description="Regex to match site IDs to apply the maintenance period to")


class UpdateMaintenancePeriodResponse(BaseModel):
    id: str = Field(description="ID of the updated maintenance period")


# --- Delete Maintenance Period --- #

class DeleteMaintenancePeriodRequest(BaseModel):
    id: str = Field(description="ID of the maintenance period to delete")


class DeleteMaintenancePeriodResponse(BaseModel):
    id: str = Field(description="ID of the deleted maintenance period")
