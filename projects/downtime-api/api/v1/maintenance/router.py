from context import Context
from api.v1.maintenance.schemas import (
    GetMaintenancePeriodByIdRequest,
    GetMaintenancePeriodsRequest,
    GetMaintenancePeriodResponse,
    CreateMaintenancePeriodRequest,
    CreateMaintenancePeriodResponse,
    UpdateMaintenancePeriodRequest,
    UpdateMaintenancePeriodResponse,
    DeleteMaintenancePeriodRequest,
    DeleteMaintenancePeriodResponse,
)

def setup_routes(prefix: str, context: Context):
    @context.app.get(prefix)
    def get_maintenance_periods(options: GetMaintenancePeriodsRequest) -> list[GetMaintenancePeriodResponse]:
        return context.maintenance_controller.get_maintenance_periods(options)

    @context.app.get(f"{prefix}/<id>")
    def get_maintenance_period_by_id(options: GetMaintenancePeriodByIdRequest) -> GetMaintenancePeriodResponse:
        return context.maintenance_controller.get_maintenance_period_by_id(options)

    @context.app.post(prefix)
    def create_maintenance_period(options: CreateMaintenancePeriodRequest) -> CreateMaintenancePeriodResponse:
        return context.maintenance_controller.create_maintenance_period(options)
    
    @context.app.put(prefix)
    def update_maintenance_period(options: UpdateMaintenancePeriodRequest) -> UpdateMaintenancePeriodResponse:
        return context.maintenance_controller.update_maintenance_period(options)
    
    @context.app.delete(prefix)
    def delete_maintenance_period(options: DeleteMaintenancePeriodRequest) -> DeleteMaintenancePeriodResponse:
        return context.maintenance_controller.delete_maintenance_period(options)
